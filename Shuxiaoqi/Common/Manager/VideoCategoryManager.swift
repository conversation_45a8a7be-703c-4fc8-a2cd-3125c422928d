//
//  VideoCategoryManager.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import UIKit

/// 视频分类缓存管理器
/// 提供先缓存后更新的机制，首次缓存失败时重试1次
class VideoCategoryManager {
    
    // MARK: - 单例
    static let shared = VideoCategoryManager()
    private init() {}
    
    // MARK: - 缓存相关
    private let cacheKey = "video_category_cache"
    private let cacheTimeKey = "video_category_cache_time"
    private let cacheValidDuration: TimeInterval = 24 * 60 * 60 // 24小时缓存有效期
    
    // MARK: - 内存缓存
    private var cachedCategories: [MainTypeListResponseData]?
    private var cacheTime: Date?
    
    // MARK: - 回调类型定义
    typealias CategoryCompletion = ([MainTypeListResponseData]) -> Void
    typealias CategoryFailure = (String) -> Void
    
    // MARK: - 公开方法
    
    /// 获取分类列表（先缓存后更新机制）
    /// - Parameters:
    ///   - completion: 成功回调，返回分类数据
    ///   - failure: 失败回调，返回错误信息
    func getCategories(completion: @escaping CategoryCompletion, failure: @escaping CategoryFailure) {
        // 1. 先尝试从缓存获取数据
        if let cached = getCachedCategories() {
            print("[VideoCategoryManager] 使用缓存数据，分类数量: \(cached.count)")
            completion(cached)
            
            // 2. 后台更新缓存（不重试）
            updateCacheInBackground()
        } else {
            // 3. 没有缓存，首次请求（带重试机制）
            print("[VideoCategoryManager] 无缓存数据，开始首次请求")
            fetchCategoriesWithRetry(completion: completion, failure: failure)
        }
    }
    
    /// 强制刷新分类列表
    /// - Parameters:
    ///   - completion: 成功回调
    ///   - failure: 失败回调
    func refreshCategories(completion: @escaping CategoryCompletion, failure: @escaping CategoryFailure) {
        print("[VideoCategoryManager] 强制刷新分类列表")
        fetchCategoriesWithRetry(completion: completion, failure: failure)
    }
    
    /// 清除缓存
    func clearCache() {
        cachedCategories = nil
        cacheTime = nil
        UserDefaults.standard.removeObject(forKey: cacheKey)
        UserDefaults.standard.removeObject(forKey: cacheTimeKey)
        print("[VideoCategoryManager] 缓存已清除")
    }
    
    // MARK: - 私有方法
    
    /// 从缓存获取分类数据
    private func getCachedCategories() -> [MainTypeListResponseData]? {
        // 1. 优先使用内存缓存
        if let cached = cachedCategories, let time = cacheTime {
            if Date().timeIntervalSince(time) < cacheValidDuration {
                return cached
            }
        }
        
        // 2. 尝试从本地存储获取
        guard let data = UserDefaults.standard.data(forKey: cacheKey),
              let cacheTimeInterval = UserDefaults.standard.object(forKey: cacheTimeKey) as? TimeInterval else {
            return nil
        }
        
        let cacheDate = Date(timeIntervalSince1970: cacheTimeInterval)
        
        // 检查缓存是否过期
        if Date().timeIntervalSince(cacheDate) >= cacheValidDuration {
            print("[VideoCategoryManager] 缓存已过期")
            return nil
        }
        
        // 解析缓存数据
        do {
            let categories = try JSONDecoder().decode([MainTypeListResponseData].self, from: data)
            // 更新内存缓存
            cachedCategories = categories
            cacheTime = cacheDate
            return categories
        } catch {
            print("[VideoCategoryManager] 缓存数据解析失败: \(error)")
            return nil
        }
    }
    
    /// 保存分类数据到缓存
    private func saveToCache(_ categories: [MainTypeListResponseData]) {
        do {
            let data = try JSONEncoder().encode(categories)
            let currentTime = Date()
            
            UserDefaults.standard.set(data, forKey: cacheKey)
            UserDefaults.standard.set(currentTime.timeIntervalSince1970, forKey: cacheTimeKey)
            
            // 更新内存缓存
            cachedCategories = categories
            cacheTime = currentTime
            
            print("[VideoCategoryManager] 分类数据已缓存，数量: \(categories.count)")
        } catch {
            print("[VideoCategoryManager] 缓存保存失败: \(error)")
        }
    }
    
    /// 带重试机制的分类获取（首次请求使用）
    private func fetchCategoriesWithRetry(completion: @escaping CategoryCompletion, failure: @escaping CategoryFailure, retryCount: Int = 0) {
        let maxRetries = 1 // 最多重试1次
        
        APIManager.shared.getVideoTypeList(isHomeShow: false) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    guard response.isSuccess, let data = response.data, !data.isEmpty else {
                        // 请求成功但数据为空或无效
                        if retryCount < maxRetries {
                            print("[VideoCategoryManager] 数据无效，重试第\(retryCount + 1)次")
                            self?.fetchCategoriesWithRetry(completion: completion, failure: failure, retryCount: retryCount + 1)
                        } else {
                            let errorMsg = "获取分类列表失败：数据为空"
                            print("[VideoCategoryManager] \(errorMsg)")
                            failure(errorMsg)
                        }
                        return
                    }
                    
                    // 请求成功，保存到缓存
                    self?.saveToCache(data)
                    print("[VideoCategoryManager] 分类列表获取成功，数量: \(data.count)")
                    completion(data)
                    
                case .failure(let error):
                    // 网络请求失败
                    if retryCount < maxRetries {
                        print("[VideoCategoryManager] 请求失败，重试第\(retryCount + 1)次: \(error.localizedDescription)")
                        self?.fetchCategoriesWithRetry(completion: completion, failure: failure, retryCount: retryCount + 1)
                    } else {
                        let errorMsg = "获取分类列表失败：\(error.localizedDescription)"
                        print("[VideoCategoryManager] \(errorMsg)")
                        failure(errorMsg)
                    }
                }
            }
        }
    }
    
    /// 后台更新缓存（不重试，静默失败）
    private func updateCacheInBackground() {
        APIManager.shared.getVideoTypeList(isHomeShow: false) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    guard response.isSuccess, let data = response.data, !data.isEmpty else {
                        print("[VideoCategoryManager] 后台更新：数据无效，忽略")
                        return
                    }
                    
                    // 更新缓存
                    self?.saveToCache(data)
                    print("[VideoCategoryManager] 后台更新缓存成功，数量: \(data.count)")
                    
                case .failure(let error):
                    print("[VideoCategoryManager] 后台更新失败（忽略）: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - 便捷方法扩展
extension VideoCategoryManager {
    
    /// 获取格式化的分类标题和ID数组（用于UI显示）
    /// - Parameters:
    ///   - includeDefault: 是否包含"请选择"默认选项
    ///   - completion: 成功回调，返回(titles, ids)
    ///   - failure: 失败回调
    func getFormattedCategories(includeDefault: Bool = true, 
                              completion: @escaping (([String], [Int]) -> Void), 
                              failure: @escaping CategoryFailure) {
        getCategories { categories in
            var titles = categories.compactMap { $0.typeName }
            var ids = categories.compactMap { $0.id }
            
            if includeDefault {
                titles.insert("请选择", at: 0)
                ids.insert(-1, at: 0)
            }
            
            completion(titles, ids)
        } failure: { error in
            failure(error)
        }
    }
}
