//
//  AttentionViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/19.
//

import UIKit
import SnapKit

// 首页关注 - 改为双列瀑布流展示
class AttentionViewController: BaseViewController {

    // MARK: - Properties
    private var gridVC: NormalListViewController?
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        // 在 super.viewDidLoad() 之前启用完全自定义布局，避免 BaseViewController 添加默认导航栏
        useFullCustomLayout = true
        super.viewDidLoad()

        // 确保不会触发TabBar的显示/隐藏
        isTabBarRootViewController = false

        // 嵌入双列网格页
        setupGridDisplay()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 手动触发关注页面的数据加载
        print("AttentionViewController: viewDidAppear - 手动触发数据加载")
        gridVC?.listDidAppear()
    }
    
    // MARK: - Setup
    private func setupGridDisplay() {
        // 复用 NormalListViewController，title 用于 mock 数据
        let vc = NormalListViewController(id: 2, title: "关注", isAttention: true)
        addChild(vc)
        view.addSubview(vc.view)
        vc.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        vc.didMove(toParent: self)
        self.gridVC = vc
    }
    
    // 覆盖 updateTabBarVisibility 方法，确保不会触发TabBar的隐藏
    override func updateTabBarVisibility() {
        // 不执行任何操作，避免触发TabBar的隐藏
        print("AttentionViewController: 跳过TabBar可见性更新")
    }
}

