//
//  NormalListViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/16.
//

import UIKit
import JXSegmentedView
import MJRefresh

// 同样修改 NormalListViewController 类
class NormalListViewController: UIViewController, JXSegmentedListContainerViewListDelegate, VideoCollectionCellDelegate {
    // 实现视频点击代理方法
    func videoCollectionCell(_ cell: VideoCollectionCell, didSelectItemAt index: Int, title: String) {
        // 这个代理方法暂时保留，但实际的点击处理在 didSelectItemAt 中
        // 如果需要，可以在这里添加额外的处理逻辑
        print("[NormalList] VideoCollectionCell 代理方法被调用，索引: \(index), 标题: \(title)")
    }
    
    private var collectionView: UICollectionView!
    private var sectionTitle: String = ""
    
    // 模拟数据（普通模式）
    private var searchData: [[String: String]] = []
    // 关注模式下的视频数据
    private var videoItems: [VideoItem] = []
    // 分类模式下的视频分组数据
    private var videoGroups: [VideoGroup] = []
    
    // 分页/加载相关属性
    private var currentPage: Int = 0
    private let pageSize: Int = 10
    private var totalDataCount: Int = 40 // 普通模式假数据
    private var isLoadingData: Bool = false
    
    // 标记是否为关注模式
    private let isAttentionMode: Bool
    // 标记是否为同城模式
    private let isCityMode: Bool
    // 新增：同城模式下的areaCode
    private var areaCode: String?
    // 分类ID，用于调用getMainWorksInfo接口
    private let categoryId: Int

    /// 空数据占位视图
    private lazy var emptyStateView: UIView = {
        let v = UIView()
        let img = UIImageView(image: UIImage(named: "empty_data_placeholder_image"))
        img.contentMode = .scaleAspectFit
        img.translatesAutoresizingMaskIntoConstraints = false
        v.addSubview(img)

        let lbl = UILabel()
        if isAttentionMode {
            lbl.text = "暂无关注的视频"
        } else if isCityMode {
            lbl.text = "暂无同城视频"
        } else {
            lbl.text = "暂无相关视频"
        }
        lbl.textColor = .lightGray
        lbl.font = .systemFont(ofSize: 14)
        lbl.translatesAutoresizingMaskIntoConstraints = false
        v.addSubview(lbl)

        NSLayoutConstraint.activate([
            img.centerXAnchor.constraint(equalTo: v.centerXAnchor),
            img.centerYAnchor.constraint(equalTo: v.centerYAnchor, constant: -20),
            img.widthAnchor.constraint(equalToConstant: 120),
            img.heightAnchor.constraint(equalToConstant: 120),
            lbl.topAnchor.constraint(equalTo: img.bottomAnchor, constant: 12),
            lbl.centerXAnchor.constraint(equalTo: v.centerXAnchor)
        ])
        return v
    }()

    init(id: Int, title: String, isAttention: Bool = false, isCity: Bool = false, areaCode: String? = nil) {
        self.sectionTitle = title
        self.categoryId = id
        assert(!(isAttention && isCity), "isAttention and isCity cannot both be true")
        self.isAttentionMode = isAttention
        self.isCityMode = isCity
        self.areaCode = areaCode
        super.init(nibName: nil, bundle: nil)
        // 不要在这里加载数据，避免collectionView为nil时崩溃
    }
    // 外部可动态更新areaCode
    func updateAreaCode(_ code: String?) {
        print("[NormalListViewController] updateAreaCode called with: \(code ?? "nil")")
        self.areaCode = code
        // 重新加载同城数据
        if isCityMode {
            loadPageData(reset: true)
        }
    }
    
    private func generateMockData() {
        // 根据不同分类生成不同数量的数据
        let count = Int.random(in: 10...20)
        for i in 0..<count {
            searchData.append([
                "title": "\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)\(sectionTitle)标题\(i+1)",
                "type": ["热门", "推荐", "精选", "普通"][Int.random(in: 0...3)],
                "duration": String(format: "%02d:%02d", Int.random(in: 0...59), Int.random(in: 0...59)),
                "author": "作者\(i+1)"
            ])
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置背景色
        view.backgroundColor = .appBackgroundGray
        
        // 设置集合视图
        setupCollectionView()
        
        // 初始设置内容边距
        updateCollectionViewInsets()
        
        // 添加MJRefresh下拉刷新
        collectionView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(handlePullToRefresh))
        // 添加MJRefresh上拉加载
        collectionView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(handleLoadMore))
        // 设置底部偏移量，保证无更多脚标能完整展示
        collectionView.contentInset.bottom = 32
        collectionView.scrollIndicatorInsets.bottom = 32
        // 注意：不在 viewDidLoad 中直接加载数据，而是等待 listDidAppear 回调
        // 这样可以确保只有当页面真正显示时才加载数据，避免不必要的网络请求
        print("NormalListViewController: viewDidLoad 完成 - \(sectionTitle)，等待 listDidAppear 触发数据加载")
    }
    
    @objc private func handlePullToRefresh() {
        reloadData { [weak self] in
            self?.collectionView.mj_header?.endRefreshing()
            self?.collectionView.mj_footer?.resetNoMoreData()
        }
    }
    
    @objc private func handleLoadMore() {
        guard !isLoadingData else { return }
        loadPageData(reset: false)
    }

    // 根据模式加载数据
    private func loadPageData(reset: Bool) {
        if isAttentionMode {
            loadAttentionData(reset: reset)
        } else if isCityMode {
            loadCityData(reset: reset)
        } else {
            loadCategoryData(reset: reset)
        }
    }

    // MARK: - 空白状态管理
    private func updateEmptyState() {
        let isEmpty: Bool
        if isAttentionMode || isCityMode {
            isEmpty = videoItems.isEmpty
        } else {
            isEmpty = videoGroups.flatMap { $0.videos }.isEmpty
        }
        collectionView.backgroundView = isEmpty ? emptyStateView : nil
    }

    // MARK: - 关注模式数据加载
    private func loadAttentionData(reset: Bool) {
        print("NormalListViewController: loadAttentionData - reset: \(reset), 当前数据量: \(videoItems.count)")
        isLoadingData = true
        if reset {
            videoItems.removeAll()
        }
        // 关注流：固定加载10条（与推荐流保持一致）
        let size = 10
        // 获取最后一条视频的信息用于翻页
        let lastVideo = reset ? nil : videoItems.last
        let createTime = lastVideo?.createTime
        let fromId = lastVideo?.id

        print("NormalListViewController: 开始请求关注视频 - size: \(size), createTime: \(createTime ?? "nil"), fromId: \(fromId ?? 0)")
        APIManager.shared.getFollowWorksList(size: size, createTime: createTime, fromId: fromId) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoadingData = false
                switch result {
                case .success(let response):
                    guard response.isSuccess, let list = response.data else {
                        print("AttentionList: API失败 - \(response.displayMessage)")
                        self.collectionView.mj_footer?.endRefreshing()
                        return
                    }
                    if reset {
                        self.videoItems = list
                    } else {
                        self.videoItems.append(contentsOf: list)
                    }
                    self.collectionView.reloadData()
                    self.updateEmptyState()
                    // 判断是否还有更多（简单依据返回条目数）
                    if list.isEmpty || list.count < size {
                        self.collectionView.mj_footer?.endRefreshingWithNoMoreData()
                    } else {
                        self.collectionView.mj_footer?.endRefreshing()
                    }
                case .failure(let error):
                    print("AttentionList: 请求失败 - \(error.localizedDescription)")
                    self.updateEmptyState()
                    self.collectionView.mj_footer?.endRefreshing()
                }
            }
        }
    }

    // MARK: - 分类模式数据加载
    private func loadCategoryData(reset: Bool) {
        isLoadingData = true
        if reset {
            videoGroups.removeAll()
        }

        // 调用getMainWorksInfo接口，传入分类ID
        print("[NormalListViewController] 调用getMainWorksInfo接口，分类: \(sectionTitle), worksCategoryId: \(categoryId)")
        APIManager.shared.getMainWorksInfo(worksCategoryId: categoryId) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoadingData = false
                switch result {
                case .success(let response):
                    guard response.isSuccess, let groups = response.data else {
                        print("NormalListViewController: 获取分类视频失败 - \(response.displayMessage)")
                        self.videoGroups = []
                        self.collectionView.reloadData()
                        self.updateEmptyState()
                        self.collectionView.mj_footer?.endRefreshing()
                        return
                    }

                    // 将接口数据转换为内部数据模型
                    let mappedGroups: [VideoGroup] = groups.compactMap { group in
                        let title = group.groupName
                        let isLive = (group.type?.typeName == "直播")
                        let videos: [VideoItem] = group.list ?? []
                        guard !videos.isEmpty else { return nil }
                        return VideoGroup(title: title, videos: videos, isLive: isLive)
                    }

                    if reset {
                        self.videoGroups = mappedGroups
                    } else {
                        self.videoGroups.append(contentsOf: mappedGroups)
                    }

                    self.collectionView.reloadData()
                    self.updateEmptyState()

                    // 由于这个接口不支持分页，所以一次性加载完成后就没有更多数据了
                    self.collectionView.mj_footer?.endRefreshingWithNoMoreData()
                    self.collectionView.contentInset.bottom = 48
                    self.collectionView.scrollIndicatorInsets.bottom = 48

                case .failure(let error):
                    print("NormalListViewController: 请求分类视频接口失败 - \(error.localizedDescription)")
                    self.updateEmptyState()
                    self.collectionView.mj_footer?.endRefreshing()
                }
            }
        }
    }
    
    // MARK: - 同城模式数据加载
    private func loadCityData(reset: Bool) {
        isLoadingData = true
        if reset {
            videoItems.removeAll()
        }
        // 后端修改了加载方案，不需要手动控制缓存，每次请求都会由后台计算是否加载过
        // 我们一直加载10条即可，加载到后台不返回=刷完了
        let loadNumber = 10
        let areaCodeToUse = areaCode ?? ""
        print("[NormalListViewController] loadCityData areaCodeToUse: \(areaCodeToUse), loadNumber: \(loadNumber)")
        if AuthManager.shared.isLoggedIn {
            // 已登录，调用原有接口，需适配areaCode参数
            APIManager.shared.getCityWorksList(loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                self?.handleCityListResult(result, loadNumber: loadNumber, reset: reset)
            }
        } else {
            // 未登录，调用未登录接口，需传deviceId、loadNumber、areaCode
            let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
            APIManager.shared.getMainCityWorksList(deviceId: deviceId, loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                self?.handleCityListResult(result, loadNumber: loadNumber, reset: reset)
            }
        }
    }
    // 统一处理同城接口返回
    private func handleCityListResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int, reset: Bool) {
        DispatchQueue.main.async {
            self.isLoadingData = false
            switch result {
            case .success(let response):
                guard response.isSuccess, let list = response.data else {
                    print("CityList: API失败 - \(response.displayMessage)")
                    self.collectionView.mj_footer?.endRefreshing()
                    return
                }
                if reset {
                    self.videoItems = list
                } else {
                    self.videoItems.append(contentsOf: list)
                }
                self.collectionView.reloadData()
                self.updateEmptyState()
                // 后端修改了加载方案：加载到后台不返回=刷完了
                // 当返回的数据为空时，表示已经刷完了
                if list.isEmpty {
                    self.collectionView.mj_footer?.endRefreshingWithNoMoreData()
                    print("CityList: 后台不返回数据，已刷完")
                } else {
                    self.collectionView.mj_footer?.endRefreshing()
                    print("CityList: 成功加载 \(list.count) 条数据")
                }
            case .failure(let error):
                print("CityList: 请求失败 - \(error.localizedDescription)")
                self.updateEmptyState()
                self.collectionView.mj_footer?.endRefreshing()
            }
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 在布局完成后更新内容边距
        updateCollectionViewInsets()
    }
    
    private func setupCollectionView() {
        // 创建布局
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        
        // 计算每个单元格的大小
        let width = (UIScreen.main.bounds.width - 12 * 3) / 2  // 两列布局，左右边距12，中间间距10
        let height: CGFloat = 364  // 设置合适的高度
        layout.itemSize = CGSize(width: width, height: height)
        layout.sectionInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        
        // 创建集合视图
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .appBackgroundGray

        // !!! 重新添加下面这行代码 !!!
        collectionView.register(DiscoverSearchResultCell.self, forCellWithReuseIdentifier: "DiscoverSearchResultCell")

        collectionView.dataSource = self
        collectionView.delegate = self
        
        view.addSubview(collectionView)
        
        // 设置约束
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // 简化集合视图边距更新方法
    private func updateCollectionViewInsets() {
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        collectionView.scrollIndicatorInsets = collectionView.contentInset
    }
    
    func listView() -> UIView {
        return view
    }

    func listDidAppear() {
        print("NormalListViewController: listDidAppear - \(sectionTitle) 页面显示")
        // 保证切换频道后，下拉手势立即可用
        if collectionView.contentOffset.y > 0 {
            collectionView.setContentOffset(.zero, animated: false)
        }

        // 当页面显示时，如果还没有数据，则加载数据
        let hasData: Bool
        if isAttentionMode || isCityMode {
            hasData = !videoItems.isEmpty
        } else {
            hasData = !videoGroups.isEmpty
        }

        print("NormalListViewController: listDidAppear - hasData: \(hasData), isAttentionMode: \(isAttentionMode), videoItems.count: \(videoItems.count)")

        if !hasData {
            print("NormalListViewController: 页面显示时没有数据，开始加载")
            loadPageData(reset: true)
        } else if isAttentionMode {
            // 关注模式下，即使有数据也检查是否需要刷新（可选）
            print("NormalListViewController: 关注模式已有数据，跳过自动加载")
        }

        // 如需切换频道自动刷新，可取消注释下一行
        // collectionView.mj_header?.beginRefreshing()
    }
}

// 为NormalListViewController添加集合视图的数据源和代理方法
extension NormalListViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if isAttentionMode || isCityMode {
            return videoItems.count
        } else {
            // 分类模式：将所有视频分组的视频展开为单个列表
            return videoGroups.flatMap { $0.videos }.count
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DiscoverSearchResultCell", for: indexPath) as! DiscoverSearchResultCell
        if isAttentionMode || isCityMode {
            if indexPath.row < videoItems.count {
                cell.configure(with: videoItems[indexPath.row])
            } else {
                // 防御性处理，避免崩溃，可清空cell内容或填充默认
                cell.configure(with: VideoItem())
            }
        } else {
            // 分类模式：将所有视频分组的视频展开为单个列表
            let allVideos = videoGroups.flatMap { $0.videos }
            if indexPath.row < allVideos.count {
                cell.configure(with: allVideos[indexPath.row])
            } else {
                // 防御性处理
                cell.configure(with: VideoItem())
            }
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 处理点击事件
        if isCityMode {
            // 同城模式：跳转到同城视频播放页面
            guard indexPath.row < videoItems.count else {
                print("[NormalList] 同城模式：无效的索引 \(indexPath.row), 总数: \(videoItems.count)")
                return
            }

            let selectedVideoItem = videoItems[indexPath.row]
            print("[NormalList] 同城模式：点击视频 - \(selectedVideoItem.worksTitle ?? "未知标题"), ID: \(selectedVideoItem.id ?? 0)")

            // 使用实例变量中的areaCode，如果为空则从UserDefaults获取
            let currentAreaCode = areaCode ?? UserDefaults.standard.string(forKey: "currentAreaCode") ?? ""
            print("[NormalList] 同城模式：使用areaCode: \(currentAreaCode)")

            // 创建同城视频播放控制器
            let videoVC = VideoDisplayCenterViewController(
                videoItem: selectedVideoItem,
                areaCode: currentAreaCode,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            videoVC.modalPresentationStyle = .fullScreen
            present(videoVC, animated: true)

        } else if isAttentionMode {
            // 关注模式：跳转到关注视频播放页面
            guard indexPath.row < videoItems.count else {
                print("[NormalList] 关注模式：无效的索引 \(indexPath.row), 总数: \(videoItems.count)")
                return
            }

            let selectedVideoItem = videoItems[indexPath.row]
            print("[NormalList] 关注模式：点击视频 - \(selectedVideoItem.worksTitle ?? "未知标题"), ID: \(selectedVideoItem.id ?? 0)")

            // 使用新的关注流初始化方法，会自动拼接关注视频
            let videoVC = VideoDisplayCenterViewController(
                followVideoItem: selectedVideoItem,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            videoVC.modalPresentationStyle = .fullScreen
            present(videoVC, animated: true)

        } else {
            // 分类模式：跳转到分类视频播放页面
            let allVideos = videoGroups.flatMap { $0.videos }
            guard indexPath.row < allVideos.count else {
                print("[NormalList] 分类模式：无效的索引 \(indexPath.row), 总数: \(allVideos.count)")
                return
            }

            let selectedVideoItem = allVideos[indexPath.row]
            print("[NormalList] 分类模式：点击视频 - \(selectedVideoItem.worksTitle ?? "未知标题"), ID: \(selectedVideoItem.id ?? 0)")

            // 使用推荐流模式播放分类视频
            let videoVC = VideoDisplayCenterViewController(
                videoItem: selectedVideoItem,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            videoVC.modalPresentationStyle = .fullScreen
            present(videoVC, animated: true)
        }
    }
}

// MARK: - ChannelRefreshable 实现
extension NormalListViewController: ChannelRefreshable {
    func reloadData(completion: @escaping () -> Void) {
        // 重置并加载第一页
        loadPageData(reset: true)
        if let collectionView = self.collectionView {
            collectionView.reloadData()
            updateEmptyState()
        }
        completion()
    }
}

